{"info": {"_postman_id": "09a1095b-de2a-42ce-9bea-5030516245f8", "name": "ShortLink API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "27270616"}, "item": [{"name": "Encode URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Response contains short_url or error\", function () {\r", "    pm.expect(pm.response.json()).to.have.property(\"short_url\").or.to.have.property(\"error\");\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"url\": \"https://sommalife.com/impact/\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8000/encode", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["encode"]}}, "response": []}, {"name": "Decode URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Response contains long_url or error\", function () {\r", "    pm.expect(pm.response.json()).to.have.property(\"long_url\").or.to.have.property(\"error\");\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"url\": \"http://shrt.est/000001\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8000/decode", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["decode"]}}, "response": []}]}